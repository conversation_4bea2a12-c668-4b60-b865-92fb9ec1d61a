
```eval_rst

PDF version: :download:`LVGL.pdf <LVGL.pdf>`
```

# Welcome to the documentation of LVGL!

<img src="_static/img/home_banner.jpg" style="width:100%">


<div style="margin-bottom:48px">
    <a href="intro/index.html"><img class="home-img" src="_static/img/home_1.png" alt="Get familiar with the LVGL project"></a>
    <a href="get-started/index.html"><img class="home-img" src="_static/img/home_2.png" alt="Learn the basic of LVGL and its usage on various platforms"></a>
    <a href="porting/index.html"><img class="home-img" src="_static/img/home_3.png" alt="See how to port LVGL to any platform"></a>
    <a href="overview/index.html"><img class="home-img" src="_static/img/home_4.png" alt="Learn the how LVGL works in more detail"></a>
    <a href="widgets/index.html"><img class="home-img" src="_static/img/home_5.png" alt="Take a look at the description of the available widgets"></a>
    <a href="CONTRIBUTING.html"><img class="home-img" src="_static/img/home_6.png" alt="Be part of the development of LVGL"></a>
</div>


```eval_rst
.. toctree::
   :maxdepth: 2

   intro/index
   examples
   get-started/index
   porting/index
   overview/index
   widgets/index
   layouts/index
   libs/index
   others/index
   CONTRIBUTING
   CHANGELOG
   ROADMAP
```
