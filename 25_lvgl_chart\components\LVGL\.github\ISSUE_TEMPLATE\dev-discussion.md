---
name: Development discussion
about: Discussion strictly related to the development of the LVGL.
title: ''
labels: ''
assignees: ''

---
<!--
IMPORTANT
Issues that don't use this template will be ignored and closed.

Normal Feature requests should go to the Forum: https://forum.lvgl.io/c/feature-request/9
-->

### Introduce the problem
<!--
A clear and concise description of the problem.
-->

### Examples and cases
<!--
Mention some examples and cases where the problem or the missing feature is relevant
-->

### Suggested solution
<!--
If you already have an idea about the solution share it here
-->
