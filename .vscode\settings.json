{"C_Cpp.intelliSenseEngine": "default", "idf.espIdfPathWin": "d:\\ESP-IDF\\Espressif\\frameworks\\esp-idf-v5.4.1", "idf.pythonInstallPath": "D:\\ESP-IDF\\Espressif\\tools\\idf-python\\3.11.2\\python.exe", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.portWin": "COM7", "idf.toolsPathWin": "D:\\ESP-IDF\\Espressif", "idf.customExtraVars": {"IDF_TARGET": "esp32s3"}, "clangd.path": "D:\\ESP-IDF\\Espressif\\tools\\esp-clang\\esp-18.1.2_20240912\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=D:\\ESP-IDF\\Espressif\\tools\\xtensa-esp-elf\\esp-14.2.0_20241119\\xtensa-esp-elf\\bin\\xtensa-esp32-elf-gcc.exe", "--compile-commands-dir=${workspaceFolder}\\build"], "idf.flashType": "UART"}