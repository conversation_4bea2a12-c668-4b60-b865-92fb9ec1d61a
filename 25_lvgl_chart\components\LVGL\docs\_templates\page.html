{% extends "!page.html" %}

{% block footer %}

<style>
  .wy-side-nav-search > div[role="search"] {
    color: black;
  }
</style>
 <script type="text/javascript">
    $(document).ready(function() {
        $(".toggle > *").hide();
        $(".toggle .header").show();
        $(".toggle .header").click(function() {
            $(this).parent().children().not(".header").toggle(400);
            $(this).parent().children(".header").toggleClass("open");
        })
    });
</script>

<script type="text/javascript">
function add_version_selector()
{
    return fetch("https://raw.githubusercontent.com/lvgl/docs_compiled/gh-pages/versionlist.txt")
        .then(res => res.text())
        .then(text => {
            const versions = text.split("\n").filter(version => version.trim().length > 0);
            let p = document.getElementById("rtd-search-form").parentElement;
            p.innerHTML = `
            <select name="versions" id="versions" onchange="ver_sel()" style="border-radius:5px; margin-bottom:15px">
            ${versions.map(version => {
                let versionName = "";
                if(version == "master") versionName = "master (latest)";
                else versionName = "v" + ((version.indexOf(".") != -1) ? version : (version + " (latest minor)"));
                return `<option value="${version}">${versionName}</option>`;
            })}
            </select>` + p.innerHTML;
        });
}

function ver_sel()
{
  var x = document.getElementById("versions").value;
  window.location.href = window.location.protocol + "//" + window.location.host + "/" + x + "/";
}

document.addEventListener('DOMContentLoaded', (event) => {
    add_version_selector().then(() => {
        var value = window.location.pathname.split('/')[1];
        document.getElementById("versions").value = value;
    });

})
document.addEventListener('DOMContentLoaded', (event) => {
    function onIntersection(entries) {
        entries.forEach(entry => {
            let currentlyLoaded = entry.target.getAttribute("data-is-loaded") == "true";
            let shouldBeLoaded = entry.intersectionRatio > 0;
            if(currentlyLoaded != shouldBeLoaded) {
                entry.target.setAttribute("data-is-loaded", shouldBeLoaded);
                if(shouldBeLoaded) {
                    let iframe = document.createElement("iframe");
                    iframe.src = entry.target.getAttribute("data-real-src");
                    entry.target.appendChild(iframe);
                } else {
                    let iframe = entry.target.querySelector("iframe");
                    iframe.parentNode.removeChild(iframe);
                }
            }
        });
    }
    const config = {
        rootMargin: '600px 0px',
        threshold: 0.01
    };
    let observer = new IntersectionObserver(onIntersection, config);
    document.querySelectorAll(".lv-example").forEach(iframe => {
        observer.observe(iframe);
    });
});
</script>
{% endblock %}
