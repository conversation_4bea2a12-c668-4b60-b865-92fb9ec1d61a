/**
 * @file lv_port_disp_templ.c
 *
 */

/*Copy this file as "lv_port_disp.c" and set this value to "1" to enable content*/
#if 1

/*********************
 *      INCLUDES
 *********************/
#include "lv_port_disp.h"
#include "lvgl.h"
#include "ltdc.h"
#include "esp_lcd_panel_ops.h"
#include "esp_err.h"
#include "esp_heap_caps.h"
#include <stdbool.h>
#include <stdio.h>

/* External declaration for RGBLCD panel handle */
extern esp_lcd_panel_handle_t panel_handle;

/*********************
 *      DEFINES
 *********************/
/* 使用最大分辨率来分配缓冲区，运行时使用实际分辨率 */
#define MAX_DISP_HOR_RES    800
#define MAX_DISP_VER_RES    480

/* 引用LTDC设备结构体 */
extern _ltdc_dev ltdcdev;

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *  STATIC PROTOTYPES
 **********************/
static void disp_init(void);

static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p);
//static void gpu_fill(lv_disp_drv_t * disp_drv, lv_color_t * dest_buf, lv_coord_t dest_width,
//        const lv_area_t * fill_area, lv_color_t color);

/**********************
 *  STATIC VARIABLES
 **********************/

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

void lv_port_disp_init(void)
{
    /*-------------------------
     * Initialize your display
     * -----------------------*/
    disp_init();

    /*-----------------------------
     * Create a buffer for drawing
     *----------------------------*/

    /* 参考25_lvgl_chart的实现方式 */
    /* 分配显示缓冲区，使用动态分配以适应不同分辨率 */
    static lv_color_t *buf1 = NULL;
    static lv_color_t *buf2 = NULL;

    /* 根据实际屏幕分辨率分配缓冲区 */
    uint32_t buf_size = ltdcdev.width * 60;  /* 60行的缓冲区 */

    buf1 = heap_caps_malloc(buf_size * sizeof(lv_color_t), MALLOC_CAP_INTERNAL);
    buf2 = heap_caps_malloc(buf_size * sizeof(lv_color_t), MALLOC_CAP_INTERNAL);

    if (buf1 == NULL || buf2 == NULL) {
        printf("Failed to allocate LVGL display buffers\n");
        /* 如果双缓冲分配失败，尝试单缓冲 */
        if (buf2) {
            heap_caps_free(buf2);
            buf2 = NULL;
        }
        if (buf1 == NULL) {
            buf_size = ltdcdev.width * 10;  /* 减少到10行 */
            buf1 = heap_caps_malloc(buf_size * sizeof(lv_color_t), MALLOC_CAP_INTERNAL);
        }
        if (buf1 == NULL) {
            printf("Critical: Cannot allocate any display buffer\n");
            return;
        }
    }

    printf("LVGL buffers allocated: buf1=%p, buf2=%p, size=%d pixels\n", buf1, buf2, buf_size);

    /* 初始化显示缓冲区 */
    static lv_disp_draw_buf_t disp_buf;
    lv_disp_draw_buf_init(&disp_buf, buf1, buf2, buf_size);

    /*-----------------------------------
     * Register the display in LVGL
     *----------------------------------*/

    static lv_disp_drv_t disp_drv;
    lv_disp_drv_init(&disp_drv);

    /* 设置显示设备的分辨率 */
    disp_drv.hor_res = ltdcdev.width;
    disp_drv.ver_res = ltdcdev.height;

    /* 用来将缓冲区的内容复制到显示设备 */
    disp_drv.flush_cb = disp_flush;

    /* 设置显示缓冲区 */
    disp_drv.draw_buf = &disp_buf;

    /* 设置用户数据为panel_handle */
    disp_drv.user_data = panel_handle;

    /* 注册显示设备 */
    lv_disp_drv_register(&disp_drv);

    printf("LVGL display driver registered: %dx%d\n", ltdcdev.width, ltdcdev.height);
}

/**********************
 *   STATIC FUNCTIONS
 **********************/

/*Initialize your display and the required peripherals.*/
static void disp_init(void)
{
    ltdc_init();
}

volatile bool disp_flush_enabled = true;

/* Enable updating the screen (the flushing process) when disp_flush() is called by LVGL
 */
void disp_enable_update(void)
{
    disp_flush_enabled = true;
}

/* Disable updating the screen (the flushing process) when disp_flush() is called by LVGL
 */
void disp_disable_update(void)
{
    disp_flush_enabled = false;
}

/*Flush the content of the internal buffer the specific area on the display
 *You can use DMA or any hardware acceleration to do this operation in the background but
 *'lv_disp_flush_ready()' has to be called when finished.*/
static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    if(disp_flush_enabled && panel_handle != NULL) {
        /*The coordinates are inclusive, so we need to add 1 to get the width/height*/
        int32_t w = area->x2 - area->x1 + 1;
        int32_t h = area->y2 - area->y1 + 1;

        /*Use RGBLCD optimized bitmap drawing function for better performance*/
        esp_err_t ret = esp_lcd_panel_draw_bitmap(panel_handle, area->x1, area->y1, area->x2 + 1, area->y2 + 1, color_p);
        if (ret != ESP_OK) {
            printf("LCD draw bitmap failed: %s, area: (%ld,%ld) to (%ld,%ld), size: %ldx%ld\n",
                   esp_err_to_name(ret), (long)area->x1, (long)area->y1, (long)area->x2, (long)area->y2, (long)w, (long)h);
        }
    }

    /*IMPORTANT!!!
     *Inform the graphics library that you are ready with the flushing*/
    lv_disp_flush_ready(disp_drv);
}

/*OPTIONAL: GPU INTERFACE*/

/*If your MCU has hardware accelerator (GPU) then you can use it to fill a memory with a color*/
//static void gpu_fill(lv_disp_drv_t * disp_drv, lv_color_t * dest_buf, lv_coord_t dest_width,
//                    const lv_area_t * fill_area, lv_color_t color)
//{
//    /*It's an example code which should be done by your GPU*/
//    int32_t x, y;
//    dest_buf += dest_width * fill_area->y1; /*Go to the first line*/
//
//    for(y = fill_area->y1; y <= fill_area->y2; y++) {
//        for(x = fill_area->x1; x <= fill_area->x2; x++) {
//            dest_buf[x] = color;
//        }
//        dest_buf+=dest_width;    /*Go to the next line*/
//    }
//}


#else /*Enable this file at the top*/

/*This dummy typedef exists purely to silence -Wpedantic.*/
typedef int keep_pedantic_happy;
#endif
