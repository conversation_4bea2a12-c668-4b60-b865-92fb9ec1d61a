# 收集music demo的源文件
file(GLOB_RECURSE MUSIC_DEMO_SRCS
    "../components/LVGL/demos/music/*.c"
)

# 收集music demo assets的源文件
file(GLOB_RECURSE MUSIC_ASSETS_SRCS
    "../components/LVGL/demos/music/assets/*.c"
)

set(include_dirs
    .
    lv_port
    ../components/LVGL/demos/music/
    ../components/LVGL/demos/music/assets
)

idf_component_register(SRCS "main.c"
                            "lv_port/lv_port_disp.c"
                            "lv_port/lv_port_fs.c"
                            "lv_port/lv_port_indev.c"
                            ${MUSIC_DEMO_SRCS}
                            ${MUSIC_ASSETS_SRCS}
                    PRIV_REQUIRES spi_flash LVGL BSP
                    INCLUDE_DIRS ${include_dirs})
