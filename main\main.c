/*
 * SPDX-FileCopyrightText: 2010-2022 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include <stdio.h>
#include <inttypes.h>
#include "sdkconfig.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_chip_info.h"
#include "esp_flash.h"
#include "esp_system.h"
#include "lvgl.h"
#include "lv_port/lv_port_disp.h"
#include "lv_port/lv_port_indev.h"
#include "lv_demos.h"

void app_main(void)
{
    printf("Starting LVGL Music Demo...\n");

    /* 初始化LVGL图形库 */
    lv_init();
    printf("LVGL initialized\n");

    /* lvgl显示接口初始化,放在lv_init()的后面 */
    lv_port_disp_init();
    printf("Display port initialized\n");

    /* lvgl输入设备初始化,放在lv_init()的后面 */
    //lv_port_indev_init();

    /* 等待一下让显示器稳定 */
    vTaskDelay(pdMS_TO_TICKS(100));

    /* 先创建一个简单的测试对象，确认显示正常 */
    lv_obj_t* test_label = lv_label_create(lv_scr_act());
    lv_label_set_text(test_label, "LVGL Test");
    lv_obj_align(test_label, LV_ALIGN_CENTER, 0, -50);
    lv_obj_set_style_text_color(test_label, lv_color_white(), 0);

    /* 等待2秒让用户看到测试标签 */
    vTaskDelay(pdMS_TO_TICKS(2000));

    /* 启动music demo */
    printf("Starting music demo...\n");
    lv_demo_music();
    printf("Music demo started\n");

    while (1)
    {
        lv_timer_handler();  // LVGL 任务管理
        vTaskDelay(pdMS_TO_TICKS(10));  // 延迟 10ms
    }
}
