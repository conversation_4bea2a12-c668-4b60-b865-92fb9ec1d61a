/*
 * SPDX-FileCopyrightText: 2010-2022 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include <stdio.h>
#include <inttypes.h>
#include "sdkconfig.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_chip_info.h"
#include "esp_flash.h"
#include "esp_system.h"
#include "lvgl.h"
#include "lv_port/lv_port_disp.h"
#include "lv_port/lv_port_indev.h"

/* 确保LVGL demo相关的宏定义已经生效 */
#if LV_USE_DEMO_MUSIC
#include "../components/LVGL/demos/music/lv_demo_music.h"
#else
#error "LV_USE_DEMO_MUSIC is not enabled in lv_conf.h"
#endif

void app_main(void)
{
    lv_init();               /* 初始化LVGL图形库 */
    lv_port_disp_init();     /* lvgl显示接口初始化,放在lv_init()的后面 */
    //lv_port_indev_init();    /* lvgl输入设备初始化,放在lv_init()的后面 */

    // /*在屏幕中间创建一个120*50大小的按钮*/
    // lv_obj_t* switch_obj = lv_switch_create(lv_scr_act());
    // lv_obj_set_size(switch_obj, 120, 50);
    // lv_obj_align(switch_obj, LV_ALIGN_CENTER, 0, 0);

    lv_demo_music();

    while (1)
    {
        lv_timer_handler();  // LVGL 任务管理
        vTaskDelay(pdMS_TO_TICKS(10));  // 延迟 10ms
    }
}
