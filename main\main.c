/*
 * SPDX-FileCopyrightText: 2010-2022 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include <stdio.h>
#include <inttypes.h>
#include "sdkconfig.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_chip_info.h"
#include "esp_flash.h"
#include "esp_system.h"
#include "lvgl.h"

void app_main(void)
{
    // LVGL 现在使用自定义时基 (esp_timer_get_time())
    // 不需要手动创建定时器调用 lv_tick_inc()

    while (1) {
        lv_timer_handler(); // LVGL 任务管理
        vTaskDelay(pdMS_TO_TICKS(10)); // 延迟 10ms
    }
}
